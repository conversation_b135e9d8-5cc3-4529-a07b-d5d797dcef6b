package com.investment.database;

import com.investment.model.Instrument;
import com.investment.model.InstrumentType;
import com.investment.model.OHLCV;
import it.unimi.dsi.fastutil.doubles.DoubleArrayList;
import java.io.File;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.Date;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DatabaseManager {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseManager.class);
    private static String DB_URL = "************************************";
    private static final String DB_USER = "sa";
    private static final String DB_PASSWORD = "";
    private static final int CURRENT_SCHEMA_VERSION = 4;

    private Connection connection;

    // For testing purposes
    public static void setDbUrl(String dbUrl) {
        DB_URL = dbUrl;
    }

    public void initDatabase() {
        try {
            // Ensure the data directory exists
            ensureDataDirectoryExists();

            // Establish connection
            connection = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            logger.info("Connected to the database");

            // Create tables if they don't exist
            createTables();

            // Run migrations if needed
            runMigrations();
        } catch (SQLException e) {
            logger.error("Error initializing database", e);
            throw new RuntimeException("Failed to initialize database", e);
        }
    }

    private void ensureDataDirectoryExists() {
        // Extract the directory path from the DB_URL
        String dbPath = DB_URL.replace("jdbc:duckdb:", "");
        // Remove the file name to get the directory
        int lastSlashIndex = dbPath.lastIndexOf('/');
        if (lastSlashIndex > 0) {
            String dirPath = dbPath.substring(0, lastSlashIndex);
            // Create the directory if it doesn't exist
            File dataDir = new File(dirPath);
            if (!dataDir.exists()) {
                boolean created = dataDir.mkdirs();
                if (created) {
                    logger.info("Created data directory: {}", dirPath);
                } else {
                    logger.warn("Failed to create data directory: {}", dirPath);
                }
            }
        }
    }

    private void createTables() throws SQLException {
        try (Statement stmt = connection.createStatement()) {
            // Create schema version table first
            stmt.execute(
                "CREATE TABLE IF NOT EXISTS schema_version (" +
                "version INTEGER PRIMARY KEY, " +
                "applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ")"
            );

            // Create instruments table with new schema
            stmt.execute(
                "CREATE TABLE IF NOT EXISTS instruments (" +
                "symbol VARCHAR(20) PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "instrument_type VARCHAR(20) NOT NULL, " +
                "market_cap DECIMAL(20,2), " +
                "country VARCHAR(50), " +
                "ipo_year INTEGER, " +
                "sector VARCHAR(100), " +
                "industry VARCHAR(100), " +
                "created_at TIMESTAMP, " +
                "updated_at TIMESTAMP" +
                ")"
            );

            // Create OHLCV data table
            stmt.execute(
                "CREATE TABLE IF NOT EXISTS ohlcv (" +
                "symbol VARCHAR(20) NOT NULL, " +
                "date DATE NOT NULL, " +
                "open DECIMAL(20,6) NOT NULL, " +
                "high DECIMAL(20,6) NOT NULL, " +
                "low DECIMAL(20,6) NOT NULL, " +
                "close DECIMAL(20,6) NOT NULL, " +
                "volume BIGINT NOT NULL, " +
                "bb_middle_band DECIMAL(20,6), " +
                "bb_std_dev DECIMAL(20,6), " +
                "bb_upper_band DECIMAL(20,6), " +
                "bb_lower_band DECIMAL(20,6), " +
                "dmi_plus_di DECIMAL(20,6), " +
                "dmi_minus_di DECIMAL(20,6), " +
                "dmi_adx DECIMAL(20,6), " +
                "PRIMARY KEY (symbol, date), " +
                "FOREIGN KEY (symbol) REFERENCES instruments(symbol)" +
                ")"
            );

            logger.info("Database tables created or already exist");
        }
    }

    private void runMigrations() throws SQLException {
        int currentVersion = getCurrentSchemaVersion();
        logger.info("Current schema version: {}", currentVersion);

        if (currentVersion < CURRENT_SCHEMA_VERSION) {
            logger.info("Running migrations from version {} to {}", currentVersion, CURRENT_SCHEMA_VERSION);

            if (currentVersion < 1) {
                migrateToVersion1();
            }
            if (currentVersion < 2) {
                migrateToVersion2();
            }
            if (currentVersion < 3) {
                migrateToVersion3();
            }
            if (currentVersion < 4) {
                migrateToVersion4();
            }

            updateSchemaVersion(CURRENT_SCHEMA_VERSION);
            logger.info("Migrations completed successfully");
        }
    }

    private int getCurrentSchemaVersion() throws SQLException {
        try (Statement stmt = connection.createStatement()) {
            // Check if schema_version table exists and has data
            try (ResultSet rs = stmt.executeQuery("SELECT MAX(version) as version FROM schema_version")) {
                if (rs.next()) {
                    Integer version = rs.getObject("version", Integer.class);
                    return version != null ? version : 0;
                }
            } catch (SQLException e) {
                // Table might not exist yet, return 0
                return 0;
            }
        }
        return 0;
    }

    private void migrateToVersion1() throws SQLException {
        logger.info("Migrating to schema version 1");
        // Version 1 is the base schema - no migration needed
        updateSchemaVersion(1);
    }

    private void migrateToVersion2() throws SQLException {
        logger.info("Migrating to schema version 2 - updating instruments table");

        try (Statement stmt = connection.createStatement()) {
            // Check if we need to migrate from old schema
            boolean hasOldSchema = false;
            try (ResultSet rs = stmt.executeQuery("SELECT column_name FROM information_schema.columns WHERE table_name = 'instruments' AND column_name = 'type'")) {
                if (rs.next()) {
                    hasOldSchema = true;
                }
            } catch (SQLException e) {
                // If information_schema doesn't work, try a different approach
                try (ResultSet rs2 = stmt.executeQuery("SELECT type FROM instruments LIMIT 0")) {
                    hasOldSchema = true; // If this succeeds, old schema exists
                } catch (SQLException e2) {
                    // Column doesn't exist, so we have new schema or no data
                    hasOldSchema = false;
                }
            }

            if (hasOldSchema) {
                logger.info("Detected old schema, performing migration");

                // Step 1: Drop foreign key constraint by dropping ohlcv table temporarily
                boolean ohlcvExists = false;
                try (ResultSet rs = stmt.executeQuery("SELECT COUNT(*) as count FROM ohlcv LIMIT 1")) {
                    ohlcvExists = true;
                } catch (SQLException e) {
                    // Table doesn't exist
                }

                List<Map<String, Object>> ohlcvData = new ArrayList<>();
                if (ohlcvExists) {
                    // Backup OHLCV data
                    try (ResultSet rs = stmt.executeQuery("SELECT * FROM ohlcv")) {
                        while (rs.next()) {
                            Map<String, Object> row = new HashMap<>();
                            row.put("symbol", rs.getString("symbol"));
                            row.put("date", rs.getDate("date"));
                            row.put("open", rs.getDouble("open"));
                            row.put("high", rs.getDouble("high"));
                            row.put("low", rs.getDouble("low"));
                            row.put("close", rs.getDouble("close"));
                            row.put("volume", rs.getLong("volume"));
                            ohlcvData.add(row);
                        }
                    }

                    // Drop OHLCV table to remove foreign key constraint
                    stmt.execute("DROP TABLE ohlcv");
                }

                // Step 2: Create new instruments table with updated schema
                // Drop any existing temp table first
                try {
                    stmt.execute("DROP TABLE IF EXISTS instruments_new");
                } catch (SQLException e) {
                    // Ignore if table doesn't exist
                }

                stmt.execute(
                    "CREATE TABLE instruments_new (" +
                    "symbol VARCHAR(20) PRIMARY KEY, " +
                    "name VARCHAR(100) NOT NULL, " +
                    "instrument_type VARCHAR(20) NOT NULL, " +
                    "market_cap DECIMAL(20,2), " +
                    "country VARCHAR(50), " +
                    "ipo_year INTEGER, " +
                    "sector VARCHAR(100), " +
                    "industry VARCHAR(100), " +
                    "created_at TIMESTAMP, " +
                    "updated_at TIMESTAMP" +
                    ")"
                );

                // Step 3: Copy data from old table to new table
                stmt.execute(
                    "INSERT INTO instruments_new (symbol, name, instrument_type, created_at, updated_at) " +
                    "SELECT symbol, name, type, created_at, updated_at FROM instruments"
                );

                // Step 4: Drop old table and rename new table
                stmt.execute("DROP TABLE instruments");
                stmt.execute("ALTER TABLE instruments_new RENAME TO instruments");

                // Step 5: Recreate OHLCV table with foreign key
                if (ohlcvExists) {
                    stmt.execute(
                        "CREATE TABLE ohlcv (" +
                        "symbol VARCHAR(20) NOT NULL, " +
                        "date DATE NOT NULL, " +
                        "open DECIMAL(20,6) NOT NULL, " +
                        "high DECIMAL(20,6) NOT NULL, " +
                        "low DECIMAL(20,6) NOT NULL, " +
                        "close DECIMAL(20,6) NOT NULL, " +
                        "volume BIGINT NOT NULL, " +
                        "bb_middle_band DECIMAL(20,6), " +
                        "bb_std_dev DECIMAL(20,6), " +
                        "bb_upper_band DECIMAL(20,6), " +
                        "bb_lower_band DECIMAL(20,6), " +
                        "PRIMARY KEY (symbol, date), " +
                        "FOREIGN KEY (symbol) REFERENCES instruments(symbol)" +
                        ")"
                    );

                    // Restore OHLCV data (without Bollinger Band data since it didn't exist in old schema)
                    for (Map<String, Object> row : ohlcvData) {
                        try (PreparedStatement pstmt = connection.prepareStatement(
                            "INSERT INTO ohlcv (symbol, date, open, high, low, close, volume, bb_middle_band, bb_std_dev, bb_upper_band, bb_lower_band) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")) {
                            pstmt.setString(1, (String) row.get("symbol"));
                            pstmt.setDate(2, (Date) row.get("date"));
                            pstmt.setDouble(3, (Double) row.get("open"));
                            pstmt.setDouble(4, (Double) row.get("high"));
                            pstmt.setDouble(5, (Double) row.get("low"));
                            pstmt.setDouble(6, (Double) row.get("close"));
                            pstmt.setLong(7, (Long) row.get("volume"));
                            // Set Bollinger Band columns to NULL for old data
                            pstmt.setNull(8, java.sql.Types.DECIMAL);
                            pstmt.setNull(9, java.sql.Types.DECIMAL);
                            pstmt.setNull(10, java.sql.Types.DECIMAL);
                            pstmt.setNull(11, java.sql.Types.DECIMAL);
                            pstmt.executeUpdate();
                        }
                    }
                }

                logger.info("Schema migration completed successfully");
            } else {
                logger.info("Schema already up to date");
            }
        }

        updateSchemaVersion(2);
    }

    private void migrateToVersion3() throws SQLException {
        logger.info("Migrating to schema version 3 - adding Bollinger Band columns to OHLCV table");

        // Check if Bollinger Band columns already exist
        boolean columnsExist = false;
        try (Statement checkStmt = connection.createStatement();
             ResultSet rs = checkStmt.executeQuery("SELECT bb_middle_band FROM ohlcv LIMIT 0")) {
            columnsExist = true; // If this succeeds, columns already exist
        } catch (SQLException e) {
            // Columns don't exist, we need to add them
            columnsExist = false;
        }

        if (!columnsExist) {
            // Add Bollinger Band columns to existing OHLCV table
            try (Statement stmt = connection.createStatement()) {
                stmt.execute("ALTER TABLE ohlcv ADD COLUMN bb_middle_band DECIMAL(20,6)");
                stmt.execute("ALTER TABLE ohlcv ADD COLUMN bb_std_dev DECIMAL(20,6)");
                stmt.execute("ALTER TABLE ohlcv ADD COLUMN bb_upper_band DECIMAL(20,6)");
                stmt.execute("ALTER TABLE ohlcv ADD COLUMN bb_lower_band DECIMAL(20,6)");

                logger.info("Successfully added Bollinger Band columns to OHLCV table");
            }
        } else {
            logger.info("Bollinger Band columns already exist in OHLCV table");
        }

        updateSchemaVersion(3);
    }

    private void migrateToVersion4() throws SQLException {
        logger.info("Migrating to schema version 4 - adding DMI columns to OHLCV table");

        // Check if DMI columns already exist
        boolean columnsExist = false;
        try (Statement checkStmt = connection.createStatement();
             ResultSet rs = checkStmt.executeQuery("SELECT dmi_plus_di FROM ohlcv LIMIT 0")) {
            columnsExist = true; // If this succeeds, columns already exist
        } catch (SQLException e) {
            // Columns don't exist, we need to add them
            columnsExist = false;
        }

        if (!columnsExist) {
            // Add DMI columns to existing OHLCV table
            try (Statement stmt = connection.createStatement()) {
                stmt.execute("ALTER TABLE ohlcv ADD COLUMN dmi_plus_di DECIMAL(20,6)");
                stmt.execute("ALTER TABLE ohlcv ADD COLUMN dmi_minus_di DECIMAL(20,6)");
                stmt.execute("ALTER TABLE ohlcv ADD COLUMN dmi_adx DECIMAL(20,6)");

                logger.info("Successfully added DMI columns to OHLCV table");
            }
        } else {
            logger.info("DMI columns already exist in OHLCV table");
        }

        updateSchemaVersion(4);
    }

    private void updateSchemaVersion(int version) throws SQLException {
        String sql = "INSERT INTO schema_version (version) VALUES (?) ON CONFLICT (version) DO NOTHING";
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setInt(1, version);
            pstmt.executeUpdate();
        }
    }

    public void saveInstrument(String symbol, String name, String type) {
        // Backward compatibility method - maps to new schema
        saveInstrumentWithDetails(symbol, name, type, null, null, null, null, null);
    }

    public void saveInstrumentWithDetails(String symbol, String name, String instrumentType,
                                        BigDecimal marketCap, String country, Integer ipoYear,
                                        String sector, String industry) {
        String sql = "INSERT INTO instruments (symbol, name, instrument_type, market_cap, country, ipo_year, sector, industry, created_at, updated_at) " +
                     "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) " +
                     "ON CONFLICT (symbol) DO UPDATE SET " +
                     "name = EXCLUDED.name, instrument_type = EXCLUDED.instrument_type, " +
                     "market_cap = EXCLUDED.market_cap, country = EXCLUDED.country, " +
                     "ipo_year = EXCLUDED.ipo_year, sector = EXCLUDED.sector, " +
                     "industry = EXCLUDED.industry, updated_at = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            java.sql.Timestamp now = new java.sql.Timestamp(System.currentTimeMillis());
            pstmt.setString(1, symbol);
            pstmt.setString(2, name);
            pstmt.setString(3, instrumentType);

            if (marketCap != null) {
                pstmt.setBigDecimal(4, marketCap);
            } else {
                pstmt.setNull(4, Types.DECIMAL);
            }

            pstmt.setString(5, country);

            if (ipoYear != null) {
                pstmt.setInt(6, ipoYear);
            } else {
                pstmt.setNull(6, Types.INTEGER);
            }

            pstmt.setString(7, sector);
            pstmt.setString(8, industry);
            pstmt.setTimestamp(9, now); // created_at
            pstmt.setTimestamp(10, now); // updated_at
            pstmt.setTimestamp(11, now); // updated_at for UPDATE

            pstmt.executeUpdate();
        } catch (SQLException e) {
            logger.error("Error saving instrument: {}", symbol, e);
            throw new RuntimeException("Failed to save instrument", e);
        }
    }

    public void saveOHLCVData(List<OHLCV> dataPoints) {
        if (dataPoints.isEmpty()) {
            return;
        }

        String sql = "INSERT INTO ohlcv (symbol, date, open, high, low, close, volume, bb_middle_band, bb_std_dev, bb_upper_band, bb_lower_band, dmi_plus_di, dmi_minus_di, dmi_adx) " +
                     "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) " +
                     "ON CONFLICT (symbol, date) DO UPDATE SET " +
                     "open = EXCLUDED.open, high = EXCLUDED.high, low = EXCLUDED.low, " +
                     "close = EXCLUDED.close, volume = EXCLUDED.volume, " +
                     "bb_middle_band = EXCLUDED.bb_middle_band, bb_std_dev = EXCLUDED.bb_std_dev, " +
                     "bb_upper_band = EXCLUDED.bb_upper_band, bb_lower_band = EXCLUDED.bb_lower_band, " +
                     "dmi_plus_di = EXCLUDED.dmi_plus_di, dmi_minus_di = EXCLUDED.dmi_minus_di, dmi_adx = EXCLUDED.dmi_adx";

        try {
            connection.setAutoCommit(false);

            try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
                for (OHLCV data : dataPoints) {
                    pstmt.setString(1, data.getSymbol());
                    pstmt.setDate(2, Date.valueOf(data.getDate()));
                    pstmt.setDouble(3, data.getOpen());
                    pstmt.setDouble(4, data.getHigh());
                    pstmt.setDouble(5, data.getLow());
                    pstmt.setDouble(6, data.getClose());
                    pstmt.setLong(7, data.getVolume());

                    // Handle nullable Bollinger Band values
                    if (data.getBbMiddleBand() != null) {
                        pstmt.setDouble(8, data.getBbMiddleBand());
                    } else {
                        pstmt.setNull(8, java.sql.Types.DECIMAL);
                    }

                    if (data.getBbStdDev() != null) {
                        pstmt.setDouble(9, data.getBbStdDev());
                    } else {
                        pstmt.setNull(9, java.sql.Types.DECIMAL);
                    }

                    if (data.getBbUpperBand() != null) {
                        pstmt.setDouble(10, data.getBbUpperBand());
                    } else {
                        pstmt.setNull(10, java.sql.Types.DECIMAL);
                    }

                    if (data.getBbLowerBand() != null) {
                        pstmt.setDouble(11, data.getBbLowerBand());
                    } else {
                        pstmt.setNull(11, java.sql.Types.DECIMAL);
                    }

                    // Handle nullable DMI values
                    if (data.getDmiPlusDi() != null) {
                        pstmt.setDouble(12, data.getDmiPlusDi());
                    } else {
                        pstmt.setNull(12, java.sql.Types.DECIMAL);
                    }

                    if (data.getDmiMinusDi() != null) {
                        pstmt.setDouble(13, data.getDmiMinusDi());
                    } else {
                        pstmt.setNull(13, java.sql.Types.DECIMAL);
                    }

                    if (data.getDmiAdx() != null) {
                        pstmt.setDouble(14, data.getDmiAdx());
                    } else {
                        pstmt.setNull(14, java.sql.Types.DECIMAL);
                    }

                    pstmt.addBatch();
                }

                int[] results = pstmt.executeBatch();
                connection.commit();

                logger.info("Saved {} OHLCV data points for {}", results.length, dataPoints.get(0).getSymbol());
            } catch (SQLException e) {
                connection.rollback();
                throw e;
            } finally {
                connection.setAutoCommit(true);
            }
        } catch (SQLException e) {
            logger.error("Error saving OHLCV data", e);
            throw new RuntimeException("Failed to save OHLCV data", e);
        }
    }

    public LocalDate getLastDataDate(String symbol) {
        String sql = "SELECT MAX(date) as last_date FROM ohlcv WHERE symbol = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    Date date = rs.getDate("last_date");
                    return date != null ? date.toLocalDate() : null;
                }
                return null;
            }
        } catch (SQLException e) {
            logger.error("Error getting last data date for symbol: {}", symbol, e);
            throw new RuntimeException("Failed to get last data date", e);
        }
    }

    /**
     * Check if a symbol has recent OHLCV data (within the last 3 days).
     * @param symbol The symbol to check
     * @return true if the symbol has recent data, false otherwise
     */
    public boolean hasRecentData(String symbol) {
        LocalDate lastDataDate = getLastDataDate(symbol);
        if (lastDataDate == null) {
            return false;
        }

        // Consider data recent if it's within the last 3 days
        LocalDate cutoffDate = LocalDate.now().minusDays(3);
        return lastDataDate.isAfter(cutoffDate) || lastDataDate.isEqual(cutoffDate);
    }

    public List<OHLCV> getOHLCVData(String symbol, LocalDate startDate, LocalDate endDate) {
        String sql = "SELECT * FROM ohlcv WHERE symbol = ? AND date BETWEEN ? AND ? ORDER BY date";
        List<OHLCV> result = new ArrayList<>();

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);
            pstmt.setDate(2, Date.valueOf(startDate));
            pstmt.setDate(3, Date.valueOf(endDate));

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    // Handle nullable Bollinger Band columns - use getDouble and check wasNull()
                    Double bbMiddleBand = null;
                    double bbMiddleValue = rs.getDouble("bb_middle_band");
                    if (!rs.wasNull()) {
                        bbMiddleBand = bbMiddleValue;
                    }

                    Double bbStdDev = null;
                    double bbStdDevValue = rs.getDouble("bb_std_dev");
                    if (!rs.wasNull()) {
                        bbStdDev = bbStdDevValue;
                    }

                    Double bbUpperBand = null;
                    double bbUpperValue = rs.getDouble("bb_upper_band");
                    if (!rs.wasNull()) {
                        bbUpperBand = bbUpperValue;
                    }

                    Double bbLowerBand = null;
                    double bbLowerValue = rs.getDouble("bb_lower_band");
                    if (!rs.wasNull()) {
                        bbLowerBand = bbLowerValue;
                    }

                    // Handle nullable DMI columns
                    Double dmiPlusDi = null;
                    double dmiPlusDiValue = rs.getDouble("dmi_plus_di");
                    if (!rs.wasNull()) {
                        dmiPlusDi = dmiPlusDiValue;
                    }

                    Double dmiMinusDi = null;
                    double dmiMinusDiValue = rs.getDouble("dmi_minus_di");
                    if (!rs.wasNull()) {
                        dmiMinusDi = dmiMinusDiValue;
                    }

                    Double dmiAdx = null;
                    double dmiAdxValue = rs.getDouble("dmi_adx");
                    if (!rs.wasNull()) {
                        dmiAdx = dmiAdxValue;
                    }

                    result.add(new OHLCV(
                        rs.getString("symbol"),
                        rs.getDate("date").toLocalDate(),
                        rs.getDouble("open"),
                        rs.getDouble("high"),
                        rs.getDouble("low"),
                        rs.getDouble("close"),
                        rs.getLong("volume"),
                        bbMiddleBand,
                        bbStdDev,
                        bbUpperBand,
                        bbLowerBand,
                        dmiPlusDi,
                        dmiMinusDi,
                        dmiAdx
                    ));
                }
            }
        } catch (SQLException e) {
            logger.error("Error retrieving OHLCV data for symbol: {}", symbol, e);
            throw new RuntimeException("Failed to retrieve OHLCV data", e);
        }

        return result;
    }

    /**
     * Get all symbols from the instruments table.
     * @return List of all symbols in the database
     */
    public List<String> getAllSymbols() {
        String sql = "SELECT symbol FROM instruments ORDER BY symbol";
        List<String> symbols = new ArrayList<>();

        try (PreparedStatement pstmt = connection.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                symbols.add(rs.getString("symbol"));
            }
        } catch (SQLException e) {
            logger.error("Error retrieving all symbols", e);
            throw new RuntimeException("Failed to retrieve symbols", e);
        }

        return symbols;
    }

    /**
     * Get unique symbols that actually have OHLCV data.
     * This is more efficient than getAllSymbols() when the instruments table
     * contains more symbols than those with actual price data.
     * @return List of symbols that have OHLCV data, ordered alphabetically
     */
    public List<String> getSymbolsWithOhlcvData() {
        String sql = "SELECT DISTINCT symbol FROM ohlcv ORDER BY symbol";
        List<String> symbols = new ArrayList<>();

        try (PreparedStatement pstmt = connection.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                symbols.add(rs.getString("symbol"));
            }

            logger.debug("Retrieved {} unique symbols with OHLCV data", symbols.size());
            return symbols;

        } catch (SQLException e) {
            logger.error("Error retrieving symbols with OHLCV data", e);
            throw new RuntimeException("Failed to retrieve symbols with OHLCV data", e);
        }
    }

    /**
     * Check if a symbol exists in the instruments table.
     * @param symbol The symbol to check
     * @return true if the symbol exists, false otherwise
     */
    public boolean symbolExists(String symbol) {
        String sql = "SELECT 1 FROM instruments WHERE symbol = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol.toUpperCase());

            try (ResultSet rs = pstmt.executeQuery()) {
                return rs.next();
            }
        } catch (SQLException e) {
            logger.error("Error checking if symbol exists: {}", symbol, e);
            throw new RuntimeException("Failed to check symbol existence", e);
        }
    }

    /**
     * Get all instruments from the database with their details.
     * @return List of all instruments in the database
     */
    public List<Instrument> getAllInstruments() {
        String sql = "SELECT symbol, name, instrument_type, market_cap, country, ipo_year, sector, industry FROM instruments ORDER BY symbol";
        List<Instrument> instruments = new ArrayList<>();

        try (PreparedStatement pstmt = connection.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                String symbol = rs.getString("symbol");
                String name = rs.getString("name");
                String instrumentTypeStr = rs.getString("instrument_type");
                BigDecimal marketCap = rs.getBigDecimal("market_cap");
                String country = rs.getString("country");
                Integer ipoYear = rs.getObject("ipo_year", Integer.class);
                String sector = rs.getString("sector");
                String industry = rs.getString("industry");

                // Parse instrument type
                InstrumentType instrumentType;
                try {
                    instrumentType = InstrumentType.valueOf(instrumentTypeStr);
                } catch (IllegalArgumentException e) {
                    logger.warn("Unknown instrument type '{}' for symbol '{}', defaulting to US_STOCK", instrumentTypeStr, symbol);
                    instrumentType = InstrumentType.US_STOCK;
                }

                instruments.add(new Instrument(symbol, name, instrumentType, marketCap, country, ipoYear, sector, industry));
            }
        } catch (SQLException e) {
            logger.error("Error retrieving all instruments", e);
            throw new RuntimeException("Failed to retrieve instruments", e);
        }

        return instruments;
    }

    /**
     * Get all instruments from the database ordered by market cap (descending).
     * Instruments with null market cap are placed at the end.
     * @return List of all instruments ordered by market cap (highest first)
     */
    public List<Instrument> getAllInstrumentsOrderedByMarketCap() {
        return getAllInstrumentsOrderedByMarketCap(0, Integer.MAX_VALUE);
    }

    /**
     * Get instruments from the database ordered by market cap (descending) with pagination.
     * Instruments with null market cap are placed at the end.
     * @param offset Starting position (0-based)
     * @param limit Maximum number of records to return
     * @return List of instruments ordered by market cap (highest first)
     */
    public List<Instrument> getAllInstrumentsOrderedByMarketCap(int offset, int limit) {
        String sql = "SELECT symbol, name, instrument_type, market_cap, country, ipo_year, sector, industry " +
                     "FROM instruments " +
                     "ORDER BY market_cap DESC NULLS LAST, symbol " +
                     "LIMIT ? OFFSET ?";
        List<Instrument> instruments = new ArrayList<>();

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setInt(1, limit);
            pstmt.setInt(2, offset);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    String symbol = rs.getString("symbol");
                    String name = rs.getString("name");
                    String instrumentTypeStr = rs.getString("instrument_type");
                    BigDecimal marketCap = rs.getBigDecimal("market_cap");
                    String country = rs.getString("country");
                    Integer ipoYear = rs.getObject("ipo_year", Integer.class);
                    String sector = rs.getString("sector");
                    String industry = rs.getString("industry");

                    // Parse instrument type
                    InstrumentType instrumentType;
                    try {
                        instrumentType = InstrumentType.valueOf(instrumentTypeStr);
                    } catch (IllegalArgumentException e) {
                        logger.warn("Unknown instrument type '{}' for symbol '{}', defaulting to US_STOCK", instrumentTypeStr, symbol);
                        instrumentType = InstrumentType.US_STOCK;
                    }

                    instruments.add(new Instrument(symbol, name, instrumentType, marketCap, country, ipoYear, sector, industry));
                }
            }
        } catch (SQLException e) {
            logger.error("Error retrieving instruments ordered by market cap with pagination (offset: {}, limit: {})", offset, limit, e);
            throw new RuntimeException("Failed to retrieve instruments", e);
        }

        return instruments;
    }

    /**
     * Get the total count of instruments in the database.
     * @return Total number of instruments
     */
    public int getTotalInstrumentCount() {
        String sql = "SELECT COUNT(*) as count FROM instruments";

        try (PreparedStatement pstmt = connection.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            if (rs.next()) {
                return rs.getInt("count");
            }
            return 0;
        } catch (SQLException e) {
            logger.error("Error counting total instruments", e);
            throw new RuntimeException("Failed to count instruments", e);
        }
    }

    /**
     * Count OHLCV records for a specific symbol.
     * @param symbol The symbol to count records for
     * @return Number of OHLCV records for the symbol
     */
    public int countOhlcvRecords(String symbol) {
        String sql = "SELECT COUNT(*) as count FROM ohlcv WHERE symbol = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("count");
                }
                return 0;
            }
        } catch (SQLException e) {
            logger.error("Error counting OHLCV records for symbol: {}", symbol, e);
            throw new RuntimeException("Failed to count OHLCV records", e);
        }
    }

    /**
     * Delete an instrument and all its associated OHLCV data.
     * This operation is transactional - either both deletions succeed or both fail.
     *
     * @param symbol The symbol to delete
     * @return Number of OHLCV records that were deleted
     */
    public int deleteInstrumentAndOhlcvData(String symbol) {
        int deletedOhlcvRecords = 0;

        try {
            connection.setAutoCommit(false);

            // First, count and delete OHLCV records
            deletedOhlcvRecords = countOhlcvRecords(symbol);
            String deleteOhlcvSql = "DELETE FROM ohlcv WHERE symbol = ?";
            try (PreparedStatement pstmt = connection.prepareStatement(deleteOhlcvSql)) {
                pstmt.setString(1, symbol);
                pstmt.executeUpdate();
            }

            // Then delete the instrument
            String deleteInstrumentSql = "DELETE FROM instruments WHERE symbol = ?";
            try (PreparedStatement pstmt = connection.prepareStatement(deleteInstrumentSql)) {
                pstmt.setString(1, symbol);
                int deletedInstruments = pstmt.executeUpdate();

                if (deletedInstruments == 0) {
                    logger.warn("No instrument found with symbol: {}", symbol);
                }
            }

            connection.commit();
            logger.info("Deleted instrument {} and {} OHLCV records", symbol, deletedOhlcvRecords);

        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException rollbackEx) {
                logger.error("Error rolling back transaction", rollbackEx);
            }
            logger.error("Error deleting instrument and OHLCV data for symbol: {}", symbol, e);
            throw new RuntimeException("Failed to delete instrument and OHLCV data", e);
        } finally {
            try {
                connection.setAutoCommit(true);
            } catch (SQLException e) {
                logger.error("Error resetting auto-commit", e);
            }
        }

        return deletedOhlcvRecords;
    }

    /**
     * Delete multiple instruments and their associated OHLCV data in a single transaction.
     *
     * @param symbols List of symbols to delete
     * @return Total number of OHLCV records that were deleted
     */
    public int deleteInstrumentsAndOhlcvData(List<String> symbols) {
        if (symbols.isEmpty()) {
            return 0;
        }

        int totalDeletedOhlcvRecords = 0;

        try {
            connection.setAutoCommit(false);

            // Count total OHLCV records that will be deleted
            for (String symbol : symbols) {
                totalDeletedOhlcvRecords += countOhlcvRecords(symbol);
            }

            // Create parameterized queries for batch deletion
            String placeholders = String.join(",", symbols.stream().map(s -> "?").toArray(String[]::new));

            // Delete OHLCV records first
            String deleteOhlcvSql = "DELETE FROM ohlcv WHERE symbol IN (" + placeholders + ")";
            try (PreparedStatement pstmt = connection.prepareStatement(deleteOhlcvSql)) {
                for (int i = 0; i < symbols.size(); i++) {
                    pstmt.setString(i + 1, symbols.get(i));
                }
                pstmt.executeUpdate();
            }

            // Then delete instruments
            String deleteInstrumentsSql = "DELETE FROM instruments WHERE symbol IN (" + placeholders + ")";
            try (PreparedStatement pstmt = connection.prepareStatement(deleteInstrumentsSql)) {
                for (int i = 0; i < symbols.size(); i++) {
                    pstmt.setString(i + 1, symbols.get(i));
                }
                int deletedInstruments = pstmt.executeUpdate();
                logger.info("Deleted {} instruments and {} OHLCV records", deletedInstruments, totalDeletedOhlcvRecords);
            }

            connection.commit();

        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException rollbackEx) {
                logger.error("Error rolling back transaction", rollbackEx);
            }
            logger.error("Error deleting instruments and OHLCV data", e);
            throw new RuntimeException("Failed to delete instruments and OHLCV data", e);
        } finally {
            try {
                connection.setAutoCommit(true);
            } catch (SQLException e) {
                logger.error("Error resetting auto-commit", e);
            }
        }

        return totalDeletedOhlcvRecords;
    }

    /**
     * Check if a symbol has existing Bollinger Band data.
     * @param symbol The symbol to check
     * @return true if the symbol has at least one record with Bollinger Band data
     */
    public boolean hasExistingBollingerBandData(String symbol) throws SQLException {
        String sql = "SELECT 1 FROM ohlcv WHERE symbol = ? AND bb_middle_band IS NOT NULL LIMIT 1";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            try (ResultSet rs = pstmt.executeQuery()) {
                return rs.next();
            }
        }
    }

    /**
     * Get the last date for which Bollinger Band data has been calculated for a symbol.
     * @param symbol The symbol to check
     * @return The last date with Bollinger Band data, or null if no data exists
     */
    public LocalDate getLastBollingerBandCalculationDate(String symbol) throws SQLException {
        String sql = "SELECT MAX(date) as last_date FROM ohlcv WHERE symbol = ? AND bb_middle_band IS NOT NULL";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    Date date = rs.getDate("last_date");
                    return date != null ? date.toLocalDate() : null;
                }
                return null;
            }
        }
    }

    /**
     * Clear existing Bollinger Band data for a symbol.
     * @param symbol The symbol to clear data for
     * @return Number of records cleared
     */
    public int clearBollingerBandData(String symbol) throws SQLException {
        String sql = "UPDATE ohlcv SET bb_middle_band = NULL, bb_std_dev = NULL, bb_upper_band = NULL, bb_lower_band = NULL WHERE symbol = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);
            int clearedRecords = pstmt.executeUpdate();
            logger.debug("Cleared Bollinger Band data for {} records of symbol: {}", clearedRecords, symbol);
            return clearedRecords;
        }
    }

    /**
     * Calculate and update Bollinger Bands incrementally for a specific symbol.
     * Only processes data points after the last calculated date.
     * @param symbol The symbol to calculate for
     * @param period The number of periods for moving average
     * @param stdDevMultiplier The standard deviation multiplier
     * @param dryRun If true, only count records that would be updated
     * @return Number of records updated (or would be updated in dry run)
     */
    public int calculateAndUpdateBollingerBandsIncremental(String symbol, int period, double stdDevMultiplier, boolean dryRun) throws SQLException {
        logger.debug("Calculating Bollinger Bands incrementally for symbol: {} (period: {}, stdDev: {}, dryRun: {})",
                    symbol, period, stdDevMultiplier, dryRun);

        // Get the last date with Bollinger Band data
        LocalDate lastCalculatedDate = getLastBollingerBandCalculationDate(symbol);

        if (lastCalculatedDate == null) {
            // No existing data, fall back to full calculation
            logger.debug("No existing Bollinger Band data for symbol: {}, performing full calculation", symbol);
            return calculateAndUpdateBollingerBands(symbol, period, stdDevMultiplier, dryRun);
        }

        // For incremental calculation, we need to recalculate from (lastDate - period + 1)
        // to ensure we have enough historical data for the moving average
        LocalDate startDate = lastCalculatedDate.minusDays(period - 1);

        if (dryRun) {
            return countRecordsForIncrementalBollingerBandCalculation(symbol, startDate);
        }

        // Use DuckDB's window functions to calculate Bollinger Bands efficiently for incremental data
        String updateSql =
            "UPDATE ohlcv SET " +
            "bb_middle_band = calculated.bb_middle_band, " +
            "bb_std_dev = calculated.bb_std_dev, " +
            "bb_upper_band = calculated.bb_upper_band, " +
            "bb_lower_band = calculated.bb_lower_band " +
            "FROM (" +
            "  SELECT " +
            "    symbol, date, " +
            "    AVG(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) AS bb_middle_band, " +
            "    STDDEV(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) AS bb_std_dev, " +
            "    AVG(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) + ? * STDDEV(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) AS bb_upper_band, " +
            "    AVG(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) - ? * STDDEV(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) AS bb_lower_band " +
            "  FROM ohlcv " +
            "  WHERE symbol = ? AND date >= ? " +
            "  ORDER BY date" +
            ") AS calculated " +
            "WHERE ohlcv.symbol = calculated.symbol AND ohlcv.date = calculated.date AND ohlcv.date > ?";

        try {
            connection.setAutoCommit(false);

            try (PreparedStatement pstmt = connection.prepareStatement(updateSql)) {
                int paramIndex = 1;
                // Set parameters for window functions (8 times for the 4 calculations)
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setDouble(paramIndex++, stdDevMultiplier); // stdDevMultiplier
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setDouble(paramIndex++, stdDevMultiplier); // stdDevMultiplier
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setString(paramIndex++, symbol); // WHERE symbol = ?
                pstmt.setDate(paramIndex++, Date.valueOf(startDate)); // WHERE date >= ?
                pstmt.setDate(paramIndex, Date.valueOf(lastCalculatedDate)); // WHERE date > ? (only update new records)

                int updatedRecords = pstmt.executeUpdate();
                connection.commit();

                logger.debug("Updated {} records with incremental Bollinger Band data for symbol: {} (from date: {})",
                           updatedRecords, symbol, lastCalculatedDate.plusDays(1));
                return updatedRecords;

            } catch (SQLException e) {
                connection.rollback();
                throw e;
            } finally {
                connection.setAutoCommit(true);
            }

        } catch (SQLException e) {
            logger.error("Error calculating incremental Bollinger Bands for symbol: {}", symbol, e);
            throw new SQLException("Failed to calculate incremental Bollinger Bands for symbol: " + symbol, e);
        }
    }

    /**
     * Calculate and update Bollinger Bands for a specific symbol using optimized SQL.
     * @param symbol The symbol to calculate for
     * @param period The number of periods for moving average (default: 20)
     * @param stdDevMultiplier The standard deviation multiplier (default: 2.0)
     * @param dryRun If true, only count records that would be updated
     * @return Number of records updated (or would be updated in dry run)
     */
    public int calculateAndUpdateBollingerBands(String symbol, int period, double stdDevMultiplier, boolean dryRun) throws SQLException {
        logger.debug("Calculating Bollinger Bands for symbol: {} (period: {}, stdDev: {}, dryRun: {})",
                    symbol, period, stdDevMultiplier, dryRun);

        if (dryRun) {
            // For dry run, just count the records that would be updated
            return countRecordsForBollingerBandCalculation(symbol, period);
        }

        // Use DuckDB's window functions to calculate Bollinger Bands efficiently
        String updateSql =
            "UPDATE ohlcv SET " +
            "bb_middle_band = calculated.bb_middle_band, " +
            "bb_std_dev = calculated.bb_std_dev, " +
            "bb_upper_band = calculated.bb_upper_band, " +
            "bb_lower_band = calculated.bb_lower_band " +
            "FROM (" +
            "  SELECT " +
            "    symbol, date, " +
            "    AVG(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) AS bb_middle_band, " +
            "    STDDEV(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) AS bb_std_dev, " +
            "    AVG(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) + ? * STDDEV(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) AS bb_upper_band, " +
            "    AVG(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) - ? * STDDEV(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) AS bb_lower_band " +
            "  FROM ohlcv " +
            "  WHERE symbol = ? " +
            "  ORDER BY date" +
            ") AS calculated " +
            "WHERE ohlcv.symbol = calculated.symbol AND ohlcv.date = calculated.date";

        try {
            connection.setAutoCommit(false);

            try (PreparedStatement pstmt = connection.prepareStatement(updateSql)) {
                int paramIndex = 1;
                // Set parameters for all the window function calculations
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setDouble(paramIndex++, stdDevMultiplier); // multiplier for upper band
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setDouble(paramIndex++, stdDevMultiplier); // multiplier for lower band
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setString(paramIndex, symbol); // WHERE symbol = ?

                int updatedRecords = pstmt.executeUpdate();
                connection.commit();

                logger.debug("Updated {} records with Bollinger Band data for symbol: {}", updatedRecords, symbol);
                return updatedRecords;

            } catch (SQLException e) {
                connection.rollback();
                throw e;
            } finally {
                connection.setAutoCommit(true);
            }

        } catch (SQLException e) {
            logger.error("Error calculating Bollinger Bands for symbol: {}", symbol, e);
            throw new SQLException("Failed to calculate Bollinger Bands for symbol: " + symbol, e);
        }
    }

    /**
     * Count records that would be updated for Bollinger Band calculation (for dry run).
     */
    private int countRecordsForBollingerBandCalculation(String symbol, int period) throws SQLException {
        String sql = "SELECT COUNT(*) as count FROM ohlcv WHERE symbol = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("count");
                }
                return 0;
            }
        }
    }

    /**
     * Count records that would be updated for incremental Bollinger Band calculation (for dry run).
     */
    private int countRecordsForIncrementalBollingerBandCalculation(String symbol, LocalDate fromDate) throws SQLException {
        String sql = "SELECT COUNT(*) as count FROM ohlcv WHERE symbol = ? AND date > ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);
            pstmt.setDate(2, Date.valueOf(fromDate));

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("count");
                }
                return 0;
            }
        }
    }

    /**
     * Check if a symbol has existing DMI data.
     * @param symbol The symbol to check
     * @return true if the symbol has at least one record with DMI data
     */
    public boolean hasExistingDMIData(String symbol) throws SQLException {
        String sql = "SELECT 1 FROM ohlcv WHERE symbol = ? AND dmi_plus_di IS NOT NULL LIMIT 1";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            try (ResultSet rs = pstmt.executeQuery()) {
                return rs.next();
            }
        }
    }

    /**
     * Get the last date for which DMI data has been calculated for a symbol.
     * @param symbol The symbol to check
     * @return The last date with DMI data, or null if no data exists
     */
    public LocalDate getLastDMICalculationDate(String symbol) throws SQLException {
        String sql = "SELECT MAX(date) as last_date FROM ohlcv WHERE symbol = ? AND dmi_plus_di IS NOT NULL";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    Date date = rs.getDate("last_date");
                    return date != null ? date.toLocalDate() : null;
                }
                return null;
            }
        }
    }

    /**
     * Clear existing DMI data for a symbol.
     * @param symbol The symbol to clear data for
     * @return Number of records cleared
     */
    public int clearDMIData(String symbol) throws SQLException {
        String sql = "UPDATE ohlcv SET dmi_plus_di = NULL, dmi_minus_di = NULL, dmi_adx = NULL WHERE symbol = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);
            int clearedRecords = pstmt.executeUpdate();
            logger.debug("Cleared DMI data for {} records of symbol: {}", clearedRecords, symbol);
            return clearedRecords;
        }
    }

    /**
     * Calculate and update DMI incrementally for a specific symbol.
     * Only processes data points after the last calculated date.
     * @param symbol The symbol to calculate for
     * @param period The number of periods for DMI calculation
     * @param dryRun If true, only count records that would be updated
     * @return Number of records updated (or would be updated in dry run)
     */
    public int calculateAndUpdateDMIIncremental(String symbol, int period, boolean dryRun) throws SQLException {
        logger.debug("Calculating DMI incrementally for symbol: {} (period: {}, dryRun: {})",
                    symbol, period, dryRun);

        // Get the last date with DMI data
        LocalDate lastCalculatedDate = getLastDMICalculationDate(symbol);

        if (lastCalculatedDate == null) {
            // No existing data, fall back to full calculation
            logger.debug("No existing DMI data for symbol: {}, performing full calculation", symbol);
            return calculateAndUpdateDMI(symbol, period, dryRun);
        }

        // For incremental calculation, we need to recalculate from (lastDate - 2*period + 1)
        // to ensure we have enough historical data for the DMI calculation
        LocalDate startDate = lastCalculatedDate.minusDays(2 * period - 1);

        if (dryRun) {
            return countRecordsForIncrementalDMICalculation(symbol, lastCalculatedDate);
        }

        // Calculate DMI for the incremental data
        return calculateDMIForDateRange(symbol, period, startDate, lastCalculatedDate.plusDays(1));
    }

    /**
     * Calculate and update DMI for a specific symbol using the complete algorithm.
     * @param symbol The symbol to calculate for
     * @param period The number of periods for DMI calculation (default: 14)
     * @param dryRun If true, only count records that would be updated
     * @return Number of records updated (or would be updated in dry run)
     */
    public int calculateAndUpdateDMI(String symbol, int period, boolean dryRun) throws SQLException {
        logger.debug("Calculating DMI for symbol: {} (period: {}, dryRun: {})", symbol, period, dryRun);

        if (dryRun) {
            // For dry run, just count the records that would be updated
            return countRecordsForDMICalculation(symbol, period);
        }

        // Calculate DMI for all data
        return calculateDMIForDateRange(symbol, period, null, null);
    }

    /**
     * Calculate DMI for a specific date range or all data if dates are null.
     * Implements the complete DMI algorithm from the provided sample code.
     */
    private int calculateDMIForDateRange(String symbol, int period, LocalDate startDate, LocalDate fromDate) throws SQLException {
        // Fetch OHLCV data for the symbol
        String fetchSql = "SELECT date, high, low, close FROM ohlcv WHERE symbol = ?";
        if (startDate != null) {
            fetchSql += " AND date >= ?";
        }
        fetchSql += " ORDER BY date";

        //DoubleArrayList highsList = new DoubleArrayList();
        List<Double> highs = new ArrayList<>();
        List<Double> lows = new ArrayList<>();
        List<Double> closes = new ArrayList<>();
        List<LocalDate> dates = new ArrayList<>();

        try (PreparedStatement pstmt = connection.prepareStatement(fetchSql)) {
            pstmt.setString(1, symbol);
            if (startDate != null) {
                pstmt.setDate(2, Date.valueOf(startDate));
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    dates.add(rs.getDate("date").toLocalDate());
                    highs.add(rs.getDouble("high"));
                    lows.add(rs.getDouble("low"));
                    closes.add(rs.getDouble("close"));
                }
            }
        }

        if (dates.size() < period * 2) {
            logger.debug("Insufficient data for DMI calculation for symbol: {} (need: {}, have: {})",
                        symbol, period * 2, dates.size());
            return 0;
        }

        // Calculate DMI using the algorithm from the sample code
        return calculateAndStoreDMI(symbol, dates, highs, lows, closes, period, fromDate);
    }

    /**
     * Core DMI calculation algorithm based on the provided sample code.
     * Calculates True Range, +DM, -DM, smoothed values, +DI, -DI, DX, and ADX.
     */
    private int calculateAndStoreDMI(String symbol, List<LocalDate> dates, List<Double> highs,
                                   List<Double> lows, List<Double> closes, int period, LocalDate fromDate) throws SQLException {

        List<Double> tr = new ArrayList<>();
        List<Double> plusDM = new ArrayList<>();
        List<Double> minusDM = new ArrayList<>();

        // Step 1: Calculate TR, +DM, and -DM
        for (int i = 0; i < closes.size(); i++) {
            if (i == 0) {
                tr.add(0.0);
                plusDM.add(0.0);
                minusDM.add(0.0);
                continue;
            }

            double upMove = highs.get(i) - highs.get(i - 1);
            double downMove = lows.get(i - 1) - lows.get(i);
            double trValue = Math.max(highs.get(i) - lows.get(i),
                    Math.max(Math.abs(highs.get(i) - closes.get(i - 1)),
                            Math.abs(lows.get(i) - closes.get(i - 1))));
            double plusDMValue = (upMove > downMove && upMove > 0) ? upMove : 0;
            double minusDMValue = (downMove > upMove && downMove > 0) ? downMove : 0;

            tr.add(trValue);
            plusDM.add(plusDMValue);
            minusDM.add(minusDMValue);
        }

        // Step 2: Calculate Smoothed TR, +DM, -DM
        List<Double> smoothedTR = calculateSmoothedValues(tr, period);
        List<Double> smoothedPlusDM = calculateSmoothedValues(plusDM, period);
        List<Double> smoothedMinusDM = calculateSmoothedValues(minusDM, period);

        // Step 3: Calculate +DI, -DI, and DX
        List<Double> plusDI = new ArrayList<>();
        List<Double> minusDI = new ArrayList<>();
        List<Double> dx = new ArrayList<>();

        for (int i = 0; i < closes.size(); i++) {
            if (smoothedTR.get(i) == null || smoothedTR.get(i) == 0) {
                plusDI.add(null);
                minusDI.add(null);
                dx.add(null);
                continue;
            }

            double pdi = (smoothedPlusDM.get(i) / smoothedTR.get(i)) * 100;
            double mdi = (smoothedMinusDM.get(i) / smoothedTR.get(i)) * 100;
            plusDI.add(pdi);
            minusDI.add(mdi);

            double sumDI = pdi + mdi;
            dx.add(sumDI == 0 ? null : (Math.abs(pdi - mdi) / sumDI) * 100);
        }

        // Step 4: Calculate ADX (smoothed DX)
        List<Double> adx = calculateADX(dx, period);

        // Step 5: Store results in database
        return storeDMIResults(symbol, dates, plusDI, minusDI, adx, fromDate);
    }

    /**
     * Calculate smoothed values using the DMI smoothing algorithm.
     */
    private List<Double> calculateSmoothedValues(List<Double> values, int period) {
        List<Double> smoothed = new ArrayList<>();

        for (int i = 0; i < values.size(); i++) {
            if (i < period) {
                smoothed.add(null);
                continue;
            }

            if (i == period) {
                // Initial sum for the first smoothed value
                double sum = 0;
                for (int j = 1; j <= period; j++) {
                    sum += values.get(i - period + j);
                }
                smoothed.add(sum);
            } else {
                // Smoothed value = previous smoothed - (previous smoothed / period) + current value
                double prevSmoothed = smoothed.get(i - 1);
                smoothed.add(prevSmoothed - (prevSmoothed / period) + values.get(i));
            }
        }

        return smoothed;
    }

    /**
     * Calculate ADX (smoothed DX values).
     */
    private List<Double> calculateADX(List<Double> dx, int period) {
        List<Double> adx = new ArrayList<>();

        for (int i = 0; i < dx.size(); i++) {
            if (i < (2 * period - 1)) { // Need period days for DX, then period for smoothing
                adx.add(null);
                continue;
            }

            if (i == (2 * period - 1)) {
                // Initial ADX calculation - average of first period DX values
                double sumDX = 0;
                int count = 0;
                for (int j = period; j <= (2 * period - 1); j++) {
                    if (dx.get(j) != null) {
                        sumDX += dx.get(j);
                        count++;
                    }
                }
                adx.add(count == 0 ? null : sumDX / count);
            } else {
                // Smoothed ADX = (previous ADX * (period-1) + current DX) / period
                Double currentDX = dx.get(i);
                Double prevADX = adx.get(i - 1);
                if (currentDX == null || prevADX == null) {
                    adx.add(null);
                } else {
                    adx.add((prevADX * (period - 1) + currentDX) / period);
                }
            }
        }

        return adx;
    }

    /**
     * Store DMI calculation results in the database.
     */
    private int storeDMIResults(String symbol, List<LocalDate> dates, List<Double> plusDI,
                              List<Double> minusDI, List<Double> adx, LocalDate fromDate) throws SQLException {

        String updateSql = "UPDATE ohlcv SET dmi_plus_di = ?, dmi_minus_di = ?, dmi_adx = ? WHERE symbol = ? AND date = ?";
        int updatedRecords = 0;

        try {
            connection.setAutoCommit(false);

            try (PreparedStatement pstmt = connection.prepareStatement(updateSql)) {
                for (int i = 0; i < dates.size(); i++) {
                    LocalDate date = dates.get(i);

                    // If fromDate is specified, only update records from that date onwards
                    if (fromDate != null && date.isBefore(fromDate)) {
                        continue;
                    }

                    Double pdi = plusDI.get(i);
                    Double mdi = minusDI.get(i);
                    Double adxVal = adx.get(i);

                    // Set parameters
                    if (pdi != null) {
                        pstmt.setDouble(1, pdi);
                    } else {
                        pstmt.setNull(1, java.sql.Types.DECIMAL);
                    }

                    if (mdi != null) {
                        pstmt.setDouble(2, mdi);
                    } else {
                        pstmt.setNull(2, java.sql.Types.DECIMAL);
                    }

                    if (adxVal != null) {
                        pstmt.setDouble(3, adxVal);
                    } else {
                        pstmt.setNull(3, java.sql.Types.DECIMAL);
                    }

                    pstmt.setString(4, symbol);
                    pstmt.setDate(5, Date.valueOf(date));

                    int rowsUpdated = pstmt.executeUpdate();
                    updatedRecords += rowsUpdated;
                }

                connection.commit();
                logger.debug("Updated {} records with DMI data for symbol: {}", updatedRecords, symbol);

            } catch (SQLException e) {
                connection.rollback();
                throw e;
            } finally {
                connection.setAutoCommit(true);
            }

        } catch (SQLException e) {
            logger.error("Error storing DMI results for symbol: {}", symbol, e);
            throw new SQLException("Failed to store DMI results for symbol: " + symbol, e);
        }

        return updatedRecords;
    }

    /**
     * Count records that would be updated for DMI calculation (for dry run).
     */
    private int countRecordsForDMICalculation(String symbol, int period) throws SQLException {
        String sql = "SELECT COUNT(*) as count FROM ohlcv WHERE symbol = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    int totalRecords = rs.getInt("count");
                    // DMI needs 2*period records minimum, so return records that would get DMI values
                    return Math.max(0, totalRecords - (2 * period - 1));
                }
                return 0;
            }
        }
    }

    /**
     * Count records that would be updated for incremental DMI calculation (for dry run).
     */
    private int countRecordsForIncrementalDMICalculation(String symbol, LocalDate fromDate) throws SQLException {
        String sql = "SELECT COUNT(*) as count FROM ohlcv WHERE symbol = ? AND date > ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);
            pstmt.setDate(2, Date.valueOf(fromDate));

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("count");
                }
                return 0;
            }
        }
    }

    public void closeConnection() {
        if (connection != null) {
            try {
                connection.close();
                logger.info("Database connection closed");
            } catch (SQLException e) {
                logger.error("Error closing database connection", e);
            }
        }
    }
}
