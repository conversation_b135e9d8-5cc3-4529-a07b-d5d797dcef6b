package com.investment.service;

import com.investment.api.model.DMIRequest;
import com.investment.api.model.DMIResponse;
import com.investment.database.DatabaseManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * Service for calculating DMI (Directional Movement Index) technical indicators.
 * Follows the same patterns as BollingerBandService for consistency.
 */
@Service
public class DMIService {

    private static final Logger logger = LoggerFactory.getLogger(DMIService.class);

    private final DatabaseManager databaseManager;

    @Autowired
    public DMIService(DatabaseManager databaseManager) {
        this.databaseManager = databaseManager;
    }

    /**
     * Calculate DMI for all symbols or a subset based on the request parameters.
     * @param request The DMI calculation request
     * @return Response containing calculation results and statistics
     */
    public DMIResponse calculateDMI(DMIRequest request) {
        logger.info("Starting DMI calculation with parameters: {}", request);
        
        long startTime = System.currentTimeMillis();
        List<String> symbolsWithInsufficientData = new ArrayList<>();
        List<String> skippedSymbols = new ArrayList<>();
        List<String> failedSymbols = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        
        int processedSymbols = 0;
        int totalRecordsUpdated = 0;

        try {
            // Get symbols to process
            List<String> symbols = getSymbolsToProcess(request);
            logger.debug("Found {} symbols to potentially process", symbols.size());

            // Determine calculation mode
            DMIRequest.CalculationMode mode = determineCalculationMode(request);
            logger.debug("Using calculation mode: {}", mode);

            for (String symbol : symbols) {
                try {
                    // Check if symbol has sufficient data
                    int recordCount = databaseManager.countOhlcvRecords(symbol);
                    if (recordCount < request.getMinDataPoints()) {
                        logger.debug("Symbol {} has insufficient data: {} records (need {})", 
                                   symbol, recordCount, request.getMinDataPoints());
                        symbolsWithInsufficientData.add(symbol);
                        continue;
                    }

                    // Check if we should skip this symbol
                    if (shouldSkipSymbol(symbol, mode, request)) {
                        logger.debug("Skipping symbol {} (already has DMI data)", symbol);
                        skippedSymbols.add(symbol);
                        continue;
                    }

                    // Calculate DMI for this symbol
                    int recordsUpdated = calculateDMIForSymbol(symbol, request, mode);
                    totalRecordsUpdated += recordsUpdated;
                    processedSymbols++;

                    logger.debug("Processed symbol {}: {} records updated", symbol, recordsUpdated);

                    logger.info("Progress: {}/{} symbols processed (index range: {}-{}), {} skipped",
                        processedSymbols, symbols.size(),
                        startIndex, startIndex + skippedSymbols + processedSymbols - 1, skippedSymbols);


                    // Check if we've reached the maximum number of symbols
                    if (request.getMaxSymbols() > 0 && processedSymbols >= request.getMaxSymbols()) {
                        logger.info("Reached maximum symbols limit: {}", request.getMaxSymbols());
                        break;
                    }

                } catch (Exception e) {
                    logger.error("Failed to calculate DMI for symbol: {}", symbol, e);
                    failedSymbols.add(symbol);
                    errors.add(String.format("Symbol %s: %s", symbol, e.getMessage()));
                }
            }

        } catch (Exception e) {
            logger.error("Error during DMI calculation", e);
            errors.add("General error: " + e.getMessage());
        }

        long processingTime = System.currentTimeMillis() - startTime;
        String status = errors.isEmpty() ? "success" : "partial_success";

        // Determine calculation mode for response
        DMIRequest.CalculationMode finalMode = determineCalculationMode(request);

        DMIResponse.CalculationParameters parameters = new DMIResponse.CalculationParameters(
                request.getPeriod(),
                request.getMinDataPoints(),
                finalMode
        );

        DMIResponse response = new DMIResponse(
                status,
                processedSymbols,
                totalRecordsUpdated,
                symbolsWithInsufficientData,
                skippedSymbols,
                failedSymbols,
                processingTime,
                errors,
                request.isDryRun(),
                parameters
        );

        logger.info("DMI calculation completed: {}", response.getSummary());
        return response;
    }

    /**
     * Get the list of symbols to process based on request parameters.
     */
    private List<String> getSymbolsToProcess(DMIRequest request) throws SQLException {
        List<String> symbols = databaseManager.getSymbolsWithOhlcvData();
        
        if (request.getMaxSymbols() > 0 && symbols.size() > request.getMaxSymbols()) {
            // Limit the number of symbols if specified
            symbols = symbols.subList(0, request.getMaxSymbols());
        }
        
        return symbols;
    }

    /**
     * Determine the calculation mode to use based on request parameters.
     */
    private DMIRequest.CalculationMode determineCalculationMode(DMIRequest request) {
        // Handle backward compatibility with deprecated forceRecalculate parameter
        if (request.isForceRecalculate()) {
            return DMIRequest.CalculationMode.FULL_RECALCULATION;
        }
        
        return request.getCalculationMode();
    }

    /**
     * Check if a symbol should be skipped based on the calculation mode.
     */
    private boolean shouldSkipSymbol(String symbol, DMIRequest.CalculationMode mode, DMIRequest request) throws SQLException {
        if (mode == DMIRequest.CalculationMode.SKIP_EXISTING) {
            return databaseManager.hasExistingDMIData(symbol);
        }
        
        // For INCREMENTAL and FULL_RECALCULATION modes, we don't skip symbols
        return false;
    }

    /**
     * Calculate DMI for a specific symbol using the specified calculation mode.
     */
    private int calculateDMIForSymbol(String symbol, DMIRequest request, DMIRequest.CalculationMode mode) throws SQLException {
        switch (mode) {
            case INCREMENTAL:
                logger.debug("Using incremental calculation mode for symbol: {}", symbol);
                return databaseManager.calculateAndUpdateDMIIncremental(
                        symbol,
                        request.getPeriod(),
                        request.isDryRun()
                );

            case FULL_RECALCULATION:
                logger.debug("Using full recalculation mode for symbol: {}", symbol);
                // Clear existing data first if not in dry run mode
                if (!request.isDryRun()) {
                    databaseManager.clearDMIData(symbol);
                }
                return databaseManager.calculateAndUpdateDMI(
                        symbol,
                        request.getPeriod(),
                        request.isDryRun()
                );

            case SKIP_EXISTING:
            default:
                logger.debug("Using skip existing mode for symbol: {}", symbol);
                return databaseManager.calculateAndUpdateDMI(
                        symbol,
                        request.getPeriod(),
                        request.isDryRun()
                );
        }
    }
}
